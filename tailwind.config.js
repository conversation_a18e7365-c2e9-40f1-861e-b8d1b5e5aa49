/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    screens: {
      xs: "375px", // Small mobile phones (iPhone SE, etc.)
      sm: "414px", // Standard mobile phones (iPhone 12, etc.)
      md: "768px", // Tablets
      lg: "1024px", // Desktop
      xl: "1280px", // Large desktop
      "2xl": "1536px", // Extra large desktop
      // Mobile-specific breakpoints
      "mobile-xs": "320px", // Very small phones
      "mobile-sm": "375px", // iPhone SE, small Android
      "mobile-md": "414px", // iPhone 12/13/14 Pro
      "mobile-lg": "428px", // iPhone 12/13/14 Pro Max
      "mobile-xl": "480px", // Large Android phones
    },
    extend: {
      fontFamily: {
        sans: ["Product Sans", "system-ui", "sans-serif"],
      },
      spacing: {
        "safe-top": "env(safe-area-inset-top)",
        "safe-bottom": "env(safe-area-inset-bottom)",
        "safe-left": "env(safe-area-inset-left)",
        "safe-right": "env(safe-area-inset-right)",
      },
      maxWidth: {
        "mobile-xs": "320px",
        "mobile-sm": "375px",
        "mobile-md": "414px",
        "mobile-lg": "428px",
        "mobile-xl": "480px",
      },
      minHeight: {
        "screen-mobile": "100dvh", // Dynamic viewport height for mobile
      },
      fontSize: {
        "mobile-xs": ["0.75rem", { lineHeight: "1rem" }],
        "mobile-sm": ["0.875rem", { lineHeight: "1.25rem" }],
        "mobile-base": ["1rem", { lineHeight: "1.5rem" }],
        "mobile-lg": ["1.125rem", { lineHeight: "1.75rem" }],
        "mobile-xl": ["1.25rem", { lineHeight: "1.75rem" }],
      },
    },
  },
  plugins: [],
};
